/* View/Edit Note Page Styles */

.pageWrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pageContent {
  flex: 1;
  padding: 2rem;
  max-width: 900px;
  margin: 0 auto;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.title {
  font-family: var(--font-montserrat);
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.headerActions {
  display: flex;
  gap: 1rem;
}

.backButton {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-family: var(--font-quicksand);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.editButton {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-family: var(--font-quicksand);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.editButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.saveButton {
  background: linear-gradient(135deg, #10ac84, #00d2d3);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-family: var(--font-quicksand);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(16, 172, 132, 0.3);
}

.saveButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 172, 132, 0.4);
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancelButton {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-family: var(--font-quicksand);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.cancelButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.noteContainer {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.noteMetadata {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.noteTitle {
  font-family: var(--font-montserrat);
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  flex: 1;
  margin-right: 1rem;
}

.noteDates {
  text-align: right;
  font-family: var(--font-quicksand);
  font-size: 0.9rem;
  color: #666;
}

.noteContent {
  font-family: var(--font-quicksand);
  font-size: 1.1rem;
  line-height: 1.7;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Edit Mode Styles */
.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-family: var(--font-montserrat);
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.titleInput {
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-family: var(--font-montserrat);
  font-size: 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  background: white;
}

.titleInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.contentTextarea {
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-family: var(--font-quicksand);
  font-size: 1.1rem;
  line-height: 1.7;
  min-height: 400px;
  resize: vertical;
  transition: all 0.3s ease;
  background: white;
}

.contentTextarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.characterCount {
  font-family: var(--font-quicksand);
  font-size: 0.9rem;
  color: #666;
  text-align: right;
  margin-top: 0.25rem;
}

.characterCount.warning {
  color: #ff6b6b;
}

/* File Upload and Attachments Styles */
.fileUploadSection {
  margin-top: 1rem;
}

.fileUploadButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-family: var(--font-quicksand);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.fileUploadButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.fileUploadButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.fileInput {
  display: none;
}

.uploadIcon {
  width: 16px;
  height: 16px;
}

.attachmentsList {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.attachmentItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  transition: all 0.2s ease;
}

.attachmentItem:hover {
  border-color: #667eea;
  background: #f0f2ff;
}

.attachmentItem.clickable {
  cursor: pointer;
}

.attachmentInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.attachmentIcon {
  width: 24px;
  height: 24px;
  color: #667eea;
  font-size: 20px;
}

.attachmentDetails {
  flex: 1;
}

.attachmentName {
  font-family: var(--font-quicksand);
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
}

.attachmentSize {
  font-family: var(--font-quicksand);
  font-size: 0.8rem;
  color: #666;
  margin: 0;
}

.removeAttachmentButton {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  font-family: var(--font-quicksand);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.removeAttachmentButton:hover {
  background: #ff5252;
}

.downloadButton {
  background: #10ac84;
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  font-family: var(--font-quicksand);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 0.5rem;
}

.downloadButton:hover {
  background: #0e9470;
}

.uploadHint {
  font-family: var(--font-quicksand);
  font-size: 0.85rem;
  color: #666;
  margin-top: 0.5rem;
  margin-bottom: 0;
}

.error {
  background: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid #fcc;
  margin-bottom: 1rem;
  font-family: var(--font-quicksand);
}

.loadingText {
  text-align: center;
  font-size: 1.2rem;
  color: #666;
  padding: 2rem;
  font-family: var(--font-quicksand);
}

/* Responsive Design */
@media (max-width: 768px) {
  .pageContent {
    padding: 1rem;
  }

  .title {
    font-size: 2rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .headerActions {
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .backButton,
  .editButton,
  .saveButton,
  .cancelButton {
    flex: 1;
    min-width: 120px;
    text-align: center;
  }

  .noteContainer {
    padding: 1.5rem;
  }

  .noteMetadata {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .noteTitle {
    font-size: 1.5rem;
    margin-right: 0;
  }

  .noteDates {
    text-align: left;
  }

  .contentTextarea {
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .pageContent {
    padding: 0.5rem;
  }

  .noteContainer {
    padding: 1rem;
  }

  .titleInput,
  .contentTextarea {
    padding: 0.75rem;
  }

  .contentTextarea {
    min-height: 250px;
  }

  .headerActions {
    flex-direction: column;
  }

  .backButton,
  .editButton,
  .saveButton,
  .cancelButton {
    width: 100%;
  }
}
