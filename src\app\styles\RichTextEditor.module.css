/* Rich Text Editor Styles */

.richTextEditor {
  display: flex;
  flex-direction: column;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  transition: all 0.3s ease;
  overflow: hidden;
}

.richTextEditor:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Toolbar */
.toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-wrap: wrap;
}

.toolbarButton {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  font-family: var(--font-quicksand);
  font-size: 0.85rem;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbarButton:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #adb5bd;
}

.toolbarButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.toolbarDivider {
  width: 1px;
  height: 20px;
  background: #dee2e6;
  margin: 0 0.5rem;
}

.toolbarHint {
  font-family: var(--font-quicksand);
  font-size: 0.8rem;
  color: #6c757d;
  font-style: italic;
  margin-left: auto;
}

/* Editor Container */
.editorContainer {
  display: flex;
  flex-direction: column;
  min-height: 400px;
  flex: 1;
  gap: 1rem;
}

/* Text Input */
.textInput {
  width: 100%;
  min-height: 200px;
  padding: 1rem;
  border: none;
  outline: none;
  background: white;
  font-family: var(--font-quicksand);
  font-size: 1rem;
  line-height: 1.6;
  color: #333;
  resize: vertical;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: border-color 0.2s ease;
}

.textInput:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.textInput::placeholder {
  color: #6c757d;
}

/* Media Display */
.mediaDisplay {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Text Content */
.textContent {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

/* Text Lines */
.textLine {
  min-height: 1.6em;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Embedded Media */
.embeddedMedia {
  margin: 1rem 0;
  display: flex;
  justify-content: center;
  pointer-events: auto;
  position: relative;
  z-index: 10;
  clear: both;
}

.mediaContainer {
  position: relative;
  max-width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background: white;
  pointer-events: auto;
}

.embeddedImage {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 8px 8px 0 0;
}

.embeddedVideo {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 8px 8px 0 0;
}

.mediaCaption {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.fileName {
  font-family: var(--font-quicksand);
  font-size: 0.85rem;
  font-weight: 500;
  color: #495057;
  flex: 1;
  margin-right: 0.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.removeMediaButton {
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.removeMediaButton:hover {
  background: #ff5252;
}

/* Quick Media Menu */
.mediaMenu {
  position: fixed;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.mediaMenuItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: white;
  border: none;
  font-family: var(--font-quicksand);
  font-size: 0.9rem;
  color: #495057;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: left;
}

.mediaMenuItem:hover {
  background: #f8f9fa;
}

/* Character Count */
.characterCount {
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  font-family: var(--font-quicksand);
  font-size: 0.85rem;
  color: #6c757d;
  text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
  .toolbar {
    padding: 0.5rem;
    gap: 0.25rem;
  }

  .toolbarButton {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }

  .toolbarHint {
    display: none;
  }

  .textInput {
    padding: 0.75rem;
    min-height: 150px;
  }

  .embeddedMedia {
    margin: 0.75rem 0;
  }

  .mediaContainer {
    max-width: 100%;
  }

  .embeddedImage,
  .embeddedVideo {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .editorContainer {
    min-height: 250px;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .toolbarButton {
    justify-content: center;
  }

  .textInput {
    padding: 0.5rem;
    min-height: 120px;
  }
}

/* Animation for media insertion */
@keyframes mediaInsert {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.embeddedMedia {
  animation: mediaInsert 0.3s ease-out;
}

/* Focus states */
.richTextEditor:focus-within .toolbar {
  background: #f0f2ff;
}

.richTextEditor:focus-within .characterCount {
  background: #f0f2ff;
  color: #495057;
}
