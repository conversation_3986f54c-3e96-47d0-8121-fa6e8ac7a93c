/* Notes List Page Styles */

.pageWrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pageContent {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.title {
  font-family: var(--font-montserrat);
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.createButton {
  background: linear-gradient(135deg, var(--primary-light, #FFB833), var(--primary, #e98e0f));
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-family: var(--font-quicksand);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.createButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.notesContainer {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.loadingText {
  text-align: center;
  font-size: 1.2rem;
  color: #666;
  padding: 2rem;
}

.error {
  background: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid #fcc;
  margin-bottom: 1rem;
}

.emptyState {
  text-align: center;
  padding: 3rem 2rem;
}

.emptyState p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 1.5rem;
}

.timeSection {
  margin-bottom: 2rem;
}

.timeSectionHeader {
  font-family: var(--font-montserrat);
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #667eea;
}

.dateGroup {
  margin-bottom: 1.5rem;
}

.dateHeader {
  font-family: var(--font-quicksand);
  font-size: 1.1rem;
  font-weight: 600;
  color: #555;
  margin-bottom: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.dateHeader:hover {
  background-color: #f8f9fa;
}

.expandIcon {
  transition: transform 0.2s ease;
}

.expandIcon.expanded {
  transform: rotate(90deg);
}

.notesList {
  display: grid;
  gap: 1rem;
  margin-left: 1.5rem;
}

.noteCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.noteCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.noteHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.noteTitle {
  font-family: var(--font-montserrat);
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  flex: 1;
  margin-right: 1rem;
}

.noteTime {
  font-family: var(--font-quicksand);
  font-size: 0.9rem;
  color: #666;
  white-space: nowrap;
}

.notePreview {
  font-family: var(--font-quicksand);
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 0.75rem;
}

.noteActions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.editButton, .deleteButton {
  padding: 0.4rem 0.8rem;
  border: none;
  border-radius: 6px;
  font-family: var(--font-quicksand);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editButton {
  background: #667eea;
  color: white;
}

.editButton:hover {
  background: #5a6fd8;
}

.deleteButton {
  background: #ff6b6b;
  color: white;
}

.deleteButton:hover {
  background: #ff5252;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pageContent {
    padding: 1rem;
  }

  .title {
    font-size: 2rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .createButton {
    width: 100%;
    text-align: center;
  }

  .noteCard {
    padding: 1rem;
  }

  .noteHeader {
    flex-direction: column;
    gap: 0.5rem;
  }

  .noteTime {
    align-self: flex-start;
  }
}
