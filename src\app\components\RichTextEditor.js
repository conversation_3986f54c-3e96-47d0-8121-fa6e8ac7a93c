'use client';

/**
 * Rich Text Editor Component
 * 
 * A rich text editor that allows users to embed images and videos
 * directly in the content with flexible positioning like Word/Notion.
 */

import { useState, useRef, useEffect } from 'react';
import styles from '@/app/styles/RichTextEditor.module.css';

const RichTextEditor = ({ 
  content, 
  onChange, 
  placeholder = "Start writing your note...",
  disabled = false,
  maxLength = 10000 
}) => {
  const [editorContent, setEditorContent] = useState(content || '');
  const [cursorPosition, setCursorPosition] = useState(0);
  const [showMediaMenu, setShowMediaMenu] = useState(false);
  const [mediaMenuPosition, setMediaMenuPosition] = useState({ x: 0, y: 0 });
  const editorRef = useRef(null);
  const fileInputRef = useRef(null);

  // Update content when prop changes
  useEffect(() => {
    if (content !== editorContent) {
      setEditorContent(content || '');
    }
  }, [content]);

  // Handle content change
  const handleContentChange = (newContent) => {
    setEditorContent(newContent);
    onChange(newContent);
  };

  // Handle key press for media insertion
  const handleKeyPress = (e) => {
    if (e.key === '/' && e.target.selectionStart === e.target.value.length) {
      // Show media menu when user types '/' at the end
      const rect = e.target.getBoundingClientRect();
      setMediaMenuPosition({
        x: rect.left + 10,
        y: rect.top + rect.height + 5
      });
      setShowMediaMenu(true);
      setCursorPosition(e.target.selectionStart);
    } else {
      setShowMediaMenu(false);
    }
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    if (!files.length) return;

    files.forEach(file => {
      // Check file size (limit to 10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert(`File "${file.name}" is too large. Maximum size is 10MB.`);
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        insertMediaAtCursor(file, reader.result);
      };

      reader.onerror = () => {
        alert(`Failed to read file "${file.name}". Please try again.`);
      };

      reader.readAsDataURL(file);
    });

    // Clear the input
    e.target.value = '';
    setShowMediaMenu(false);
  };

  // Insert media at cursor position
  const insertMediaAtCursor = (file, dataUrl) => {
    const isImage = file.type.startsWith('image/');
    const isVideo = file.type.startsWith('video/');
    
    if (!isImage && !isVideo) {
      alert('Only images and videos can be embedded in the content. Other files will be added as attachments.');
      return;
    }

    // Create media placeholder
    const mediaId = `media_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const mediaPlaceholder = isImage 
      ? `\n[IMAGE:${mediaId}:${file.name}:${dataUrl}]\n`
      : `\n[VIDEO:${mediaId}:${file.name}:${dataUrl}]\n`;

    // Insert at cursor position
    const beforeCursor = editorContent.substring(0, cursorPosition);
    const afterCursor = editorContent.substring(cursorPosition);
    const newContent = beforeCursor + mediaPlaceholder + afterCursor;

    handleContentChange(newContent);
  };

  // Render content with embedded media
  const renderContent = (text) => {
    if (!text) return '';

    // Split content by media placeholders
    const parts = text.split(/(\[(?:IMAGE|VIDEO):[^\]]+\])/g);
    
    return parts.map((part, index) => {
      // Check if this part is a media placeholder
      const mediaMatch = part.match(/\[(IMAGE|VIDEO):([^:]+):([^:]+):([^\]]+)\]/);
      
      if (mediaMatch) {
        const [, type, mediaId, fileName, dataUrl] = mediaMatch;
        
        if (type === 'IMAGE') {
          return (
            <div key={index} className={styles.embeddedMedia}>
              <div className={styles.mediaContainer}>
                <img 
                  src={dataUrl} 
                  alt={fileName}
                  className={styles.embeddedImage}
                />
                <div className={styles.mediaCaption}>
                  <span className={styles.fileName}>{fileName}</span>
                  <button 
                    className={styles.removeMediaButton}
                    onClick={() => removeMedia(part)}
                    type="button"
                  >
                    ✕
                  </button>
                </div>
              </div>
            </div>
          );
        } else if (type === 'VIDEO') {
          return (
            <div key={index} className={styles.embeddedMedia}>
              <div className={styles.mediaContainer}>
                <video 
                  src={dataUrl} 
                  controls
                  className={styles.embeddedVideo}
                />
                <div className={styles.mediaCaption}>
                  <span className={styles.fileName}>{fileName}</span>
                  <button 
                    className={styles.removeMediaButton}
                    onClick={() => removeMedia(part)}
                    type="button"
                  >
                    ✕
                  </button>
                </div>
              </div>
            </div>
          );
        }
      }
      
      // Regular text content
      return part.split('\n').map((line, lineIndex) => (
        <div key={`${index}-${lineIndex}`} className={styles.textLine}>
          {line || '\u00A0'} {/* Non-breaking space for empty lines */}
        </div>
      ));
    });
  };

  // Remove media from content
  const removeMedia = (mediaPlaceholder) => {
    const newContent = editorContent.replace(mediaPlaceholder, '');
    handleContentChange(newContent);
  };

  // Handle media menu selection
  const handleMediaMenuSelect = (type) => {
    if (type === 'image' || type === 'video') {
      fileInputRef.current.accept = type === 'image' ? 'image/*' : 'video/*';
      fileInputRef.current.click();
    }
    setShowMediaMenu(false);
  };

  return (
    <div className={styles.richTextEditor}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        onChange={handleFileUpload}
        style={{ display: 'none' }}
        disabled={disabled}
      />

      {/* Toolbar */}
      <div className={styles.toolbar}>
        <button
          type="button"
          className={styles.toolbarButton}
          onClick={() => handleMediaMenuSelect('image')}
          disabled={disabled}
          title="Insert Image"
        >
          🖼️ Image
        </button>
        <button
          type="button"
          className={styles.toolbarButton}
          onClick={() => handleMediaMenuSelect('video')}
          disabled={disabled}
          title="Insert Video"
        >
          🎥 Video
        </button>
        <div className={styles.toolbarDivider}></div>
        <span className={styles.toolbarHint}>
          Type "/" for quick insert menu
        </span>
      </div>

      {/* Editor Area */}
      <div className={styles.editorContainer}>
        {/* Preview Mode */}
        <div className={styles.previewArea}>
          {renderContent(editorContent)}
        </div>

        {/* Text Input Overlay */}
        <textarea
          ref={editorRef}
          value={editorContent}
          onChange={(e) => handleContentChange(e.target.value)}
          onKeyPress={handleKeyPress}
          onSelect={(e) => setCursorPosition(e.target.selectionStart)}
          onClick={(e) => setCursorPosition(e.target.selectionStart)}
          placeholder={placeholder}
          className={styles.textInput}
          maxLength={maxLength}
          disabled={disabled}
        />
      </div>

      {/* Quick Media Menu */}
      {showMediaMenu && (
        <div 
          className={styles.mediaMenu}
          style={{ 
            left: mediaMenuPosition.x, 
            top: mediaMenuPosition.y 
          }}
        >
          <button
            className={styles.mediaMenuItem}
            onClick={() => handleMediaMenuSelect('image')}
          >
            🖼️ Insert Image
          </button>
          <button
            className={styles.mediaMenuItem}
            onClick={() => handleMediaMenuSelect('video')}
          >
            🎥 Insert Video
          </button>
        </div>
      )}

      {/* Character Count */}
      <div className={styles.characterCount}>
        {editorContent.length}/{maxLength} characters
      </div>
    </div>
  );
};

export default RichTextEditor;
